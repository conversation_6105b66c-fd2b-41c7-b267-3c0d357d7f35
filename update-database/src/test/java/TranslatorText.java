import java.io.IOException;

/*import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.*;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;*/

public class TranslatorText {
    private static String key = "c58fdece93ba4c66ac5a7178ffc2a035";
    public String endpoint = "https://api.cognitive.microsofttranslator.com";
    public String route = "/translate?api-version=3.0&to=zh-cn&to=it";
    public String url = endpoint.concat(route);

    // location, also known as region.
    // required if you're using a multi-service or regional (not global) resource. It can be found in the Azure portal on the Keys and Endpoint page.
    /*private static String location = "northcentralus";
    // Instantiates the OkHttpClient.
    OkHttpClient client = new OkHttpClient();

    // This function performs a POST request.
    public String Post(String text) throws IOException {
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType,
                "[{\"Text\": \"" + text + "\"}]");
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Ocp-Apim-Subscription-Key", key)
                // location required if you're using a multi-service or regional (not global) resource.
                .addHeader("Ocp-Apim-Subscription-Region", location)
                .addHeader("Content-type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        return response.body().string();
    }

    // This function prettifies the json response.
    public static String prettify(String json_text) {
        JsonParser parser = new JsonParser();
        JsonElement json = parser.parse(json_text);
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        return gson.toJson(json);
    }



    public static void main(String[] args) {
        try {
            String text = "医疗设备网络安全区域事件准备和响应行动手册快速入门指南";
            TranslatorText translateRequest = new TranslatorText();
            String response = translateRequest.Post(text);
            String prettify = prettify(response);
            JSONArray array = JSON.parseArray(prettify);
            String res = array.get(0).toString();
            JSONObject object = JSON.parseObject(res);
            JSONObject detectedLanguage = object.getJSONObject("detectedLanguage");
            String language = detectedLanguage.getString("language");
            if (language.equals("en")){
                JSONArray translationsJson = object.getJSONArray("translations");
                JSONObject resData = JSON.parseObject(translationsJson.get(0).toString());
                String translations = resData.getString("text");
                System.out.println(translations);
            }
        } catch (Exception e) {
            System.out.println(e);
        }
    }*/
}