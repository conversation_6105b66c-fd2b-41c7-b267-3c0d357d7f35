package com.lirong.organization.common.vo;

import java.math.BigDecimal;

/**
 * 供应链分析VO
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
public class SupplyChainsAnalysisVO {

    /**
     * 承包商编码
     */
    private String orgCode;

    /**
     *合同金额
     */
    private BigDecimal amount;

    /**
     *合同数量
     */
    private Integer contractCount;

    /**
     *装备数量
     */
    private Integer weaponryCount;

    /**
     *参与关键节点数量
     */
    private Integer participateKeyNodeCount;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getContractCount() {
        return contractCount;
    }

    public void setContractCount(Integer contractCount) {
        this.contractCount = contractCount;
    }

    public Integer getWeaponryCount() {
        return weaponryCount;
    }

    public void setWeaponryCount(Integer weaponryCount) {
        this.weaponryCount = weaponryCount;
    }

    public Integer getParticipateKeyNodeCount() {
        return participateKeyNodeCount;
    }

    public void setParticipateKeyNodeCount(Integer participateKeyNodeCount) {
        this.participateKeyNodeCount = participateKeyNodeCount;
    }
}
