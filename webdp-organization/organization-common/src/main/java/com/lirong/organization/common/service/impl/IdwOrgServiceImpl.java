package com.lirong.organization.common.service.impl;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;

import com.lirong.common.service.CascadeDeleteService;
import com.lirong.common.utils.*;
import com.lirong.common.vo.StatisticsVO;
import com.lirong.organization.common.vo.SimpleOrg;
import com.lirong.organization.common.vo.SupplyChainsAnalysisVO;
import com.lirong.system.domain.SysParam;
import com.lirong.system.mapper.SysParamMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.service.IdwOrgService;
import com.lirong.common.core.text.Convert;
import org.springframework.transaction.annotation.Transactional;

/**
 * 组织机构Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-29
 */
@Service
@Transactional
public class IdwOrgServiceImpl implements IdwOrgService {
    @Autowired//组织机构
    private IdwOrgMapper idwOrgMapper;
    @Autowired//系统配置
    private SysParamMapper sysParamMapper;
    @Autowired//级联删除接口
    private List<CascadeDeleteService> cascadeDeleteServiceList;

    /**
     * 查询组织机构列表
     *
     * @param idwOrg 组织机构
     * @return 组织机构
     */
    @Override
    public List<IdwOrg> selectIdwOrgList(IdwOrg idwOrg) {
        return idwOrgMapper.selectIdwOrgList(idwOrg);
    }

    /**
     * 新增组织机构
     *
     * @param org 组织机构
     * @return 结果
     */
    @Override
    public int insertIdwOrg(IdwOrg org) {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        org.setCreateBy(userName);
        org.setCreateTime(nowDate);
        if (StringUtils.isBlank(org.getOrgTypeAlias())) {
            org.setOrgTypeAlias(org.getOrgType());
        }
        if (StringUtils.isNotBlank(org.getYear())) {
            org.setEstablishTime(org.getYear());
            if (StringUtils.isNotBlank(org.getMonth())) {
                org.setEstablishTime(org.getYear() + "-" + org.getMonth());
                if (StringUtils.isNotBlank(org.getDay())) {
                    org.setEstablishTime(org.getYear() + "-" + org.getMonth() + "-" + org.getDay());
                }
            }
        }
        //赋值机构编码
        long id = SnowIdUtils.uniqueLong();// 获取雪花id
        org.setOrgId(id);
        org.setOrgCode("organization_" + id);
        return idwOrgMapper.insertIdwOrg(org);
    }

    /**
     * 修改组织机构
     *
     * @param org 组织机构
     * @return 结果
     */
    @Override
    public int updateIdwOrg(IdwOrg org) {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        org.setUpdateBy(userName);
        org.setUpdateTime(nowDate);
        if (StringUtils.isNotBlank(org.getYear())) {
            org.setEstablishTime(org.getYear());
            if (StringUtils.isNotBlank(org.getMonth())) {
                org.setEstablishTime(org.getYear() + "-" + org.getMonth());
                if (StringUtils.isNotBlank(org.getDay())) {
                    org.setEstablishTime(org.getYear() + "-" + org.getMonth() + "-" + org.getDay());
                }
            }
        }
        return idwOrgMapper.updateIdwOrg(org);
    }

    /**
     * 根据机构编码删除机构信息
     *
     * @param orgCodes 机构编码
     * @return 结果
     */
    @Override
    public int deleteOrgByOrgCodes(String[] orgCodes) {
        String deleteTime = DateUtils.dateTimeNow();
        String loginName = ShiroUtils.getUserName();
        idwOrgMapper.deleteOrgByOrgCodes(orgCodes, loginName, deleteTime);
        if (StringUtils.isNotNull(orgCodes) && orgCodes.length > 0) {
            //删除机构相关纬度
            for (CascadeDeleteService cascadeDeleteService : cascadeDeleteServiceList) {
                cascadeDeleteService.deleteByCode("org", orgCodes, loginName, deleteTime);
            }
        }
        return 1;
    }

    /**
     * 根据机构编码查询
     *
     * @param orgCode 机构编码
     * @return 结果
     */
    @Override
    public IdwOrg selectOrgByOrgCode(String orgCode) {
        IdwOrg org = idwOrgMapper.selectOrgByOrgCode(orgCode);
        if (null != org) {
            Map<String, String> dateMap = DateUtils.splitDate(org.getEstablishTime());
            org.setYear(dateMap.get("year"));
            org.setMonth(dateMap.get("month"));
            org.setDay(dateMap.get("day"));
        }
        return org;
    }

    /**
     * 获取传入分类最大的排序号
     *
     * @param orgType 机构分类
     * @return 结果
     */
    @Override
    public int selectMaxOrderNum(String orgType) {
        return idwOrgMapper.selectMaxOrderNum(orgType);
    }

    /**
     * 根据关键字和机构类型(包含)查询机构信息 like查询名称(中英文名称&机构编码)
     *
     * @param orgTypes 机构类型
     * @param keyword  关键字
     * @return 结果
     */
    @Override
    public List<SimpleOrg> selectByKeywordAndIncludeOrgTypes(String orgTypes, String keyword) {
        String[] orgTypeArray;
        if (orgTypes != null) {
            orgTypeArray = Convert.toStrArray(orgTypes);
        } else {
            orgTypeArray = null;
        }
        return idwOrgMapper.selectByKeywordAndIncludeOrgTypes(orgTypeArray, keyword);
    }

    /**
     * 获取文件路径
     *
     * @param type 业务类型
     * @return 文件路径
     */
    @Override
    public List<String> getFilePath(List<String> type) {
        if (StringUtils.isNotNull(type) && type.contains("organization")) {
            return idwOrgMapper.selectAllFilePath();
        } else {
            return null;
        }
    }

    /**
     * 计算机构评分与排名
     *
     * @return 结果
     */
    @Override
    public String calculateRankingAndScore() {
        List<SysParam> sysParamList = sysParamMapper.selectSysParamByParamKey("organization:ranking:weight");
        String peopleCountWeight = "";
        String contractCountWeight = "";
        String awardCountWeight = "";
        String patentCountWeight = "";
        String organizationDocumentCountWeight = "";
        String organizationResearchFieldCountWeight = "";
        for (SysParam sysParam : sysParamList) {
            if ("员工数量".equals(sysParam.getName())) {
                peopleCountWeight = sysParam.getValue();
            } else if ("签订合同".equals(sysParam.getName())) {
                contractCountWeight = sysParam.getValue();
            } else if ("获奖产品".equals(sysParam.getName())) {
                awardCountWeight = sysParam.getValue();
            } else if ("发明专利".equals(sysParam.getName())) {
                patentCountWeight = sysParam.getValue();
            } else if ("机构文献".equals(sysParam.getName())) {
                organizationDocumentCountWeight = sysParam.getValue();
            } else if ("研究领域".equals(sysParam.getName())) {
                organizationResearchFieldCountWeight = sysParam.getValue();
            }
        }
        //计算机构评分
        List<IdwOrg> orgList = idwOrgMapper.selectScoreOrgList(false);
        //员工数量
        Integer maxPeopleCount = idwOrgMapper.selectMaxPeopleCount();
        Integer minPeopleCount = idwOrgMapper.selectMinPeopleCount();
        //签订合同
        Integer maxContractCount = idwOrgMapper.selectMaxContractCount();
        Integer minContractCount = idwOrgMapper.selectMinContractCount();
        //获奖产品
        Integer maxAwardCount = idwOrgMapper.selectMaxAwardCount();
        Integer minAwardCount = idwOrgMapper.selectMinAwardCount();
        //发明专利
        Integer maxPatentCount = idwOrgMapper.selectMaxPatentCount();
        Integer minPatentCount = idwOrgMapper.selectMinPatentCount();
        //出版作品
        Integer maxOrganizationDocumentCount = idwOrgMapper.selectMaxOrganizationDocumentCount();
        Integer minOrganizationDocumentCount = idwOrgMapper.selectMinOrganizationDocumentCount();
        //研究领域
        Integer maxOrganizationResearchFieldCount = idwOrgMapper.selectMaxOrganizationResearchFieldCount();
        Integer minOrganizationResearchFieldCount = idwOrgMapper.selectMinOrganizationResearchFieldCount();
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        for (IdwOrg org : orgList) {
            String orgName = StringUtils.isNoneBlank(org.getOrgNameEn()) ? org.getOrgNameEn() : org.getOrgNameCn();
            //员工数量
            Integer peopleCount = org.getPeopleCount();
            double peopleCountScore = 0;
            if (StringUtils.isNotNull(peopleCount) && peopleCount != 0) {
                peopleCountScore = calculateScore(peopleCount, minPeopleCount, maxPeopleCount, peopleCountWeight);
            }
            //签订合同
            Integer contractCount = idwOrgMapper.selectContractCounByUei(org.getUei());
            double contractCountScore = 0;
            if (StringUtils.isNotNull(contractCount) && contractCount != 0) {
                contractCountScore = calculateScore(contractCount, minContractCount, maxContractCount, contractCountWeight);
            }
            //获奖产品
            Integer awardCount = idwOrgMapper.selectAwardCountByOrgName(orgName);
            double awardCountScore = 0;
            if (StringUtils.isNotNull(awardCount) && awardCount != 0) {
                awardCountScore = calculateScore(awardCount, minAwardCount, maxAwardCount, awardCountWeight);
            }
            //发明专利
            Integer patentCount = idwOrgMapper.selectPatentCount(orgName);
            double patentCountScore = 0;
            if (StringUtils.isNotNull(patentCount) && patentCount != 0) {
                patentCountScore = calculateScore(patentCount, minPatentCount, maxPatentCount, patentCountWeight);
            }
            //出版作品
            Integer organizationDocumentCount = idwOrgMapper.selectOrganizationDocumentCountByOrgCode(org.getOrgCode());
            double organizationDocumentCountScore = 0;
            if (StringUtils.isNotNull(organizationDocumentCount) && organizationDocumentCount != 0) {
                organizationDocumentCountScore = calculateScore(organizationDocumentCount, minOrganizationDocumentCount, maxOrganizationDocumentCount, organizationDocumentCountWeight);
            }
            //研究领域
            Integer organizationResearchFieldCount = idwOrgMapper.selectOrganizationResearchFieldCountByOrgCode(org.getOrgCode());
            double organizationResearchFieldCountScore = 0;
            if (StringUtils.isNotNull(organizationResearchFieldCount) && organizationResearchFieldCount != 0) {
                organizationResearchFieldCountScore = calculateScore(organizationResearchFieldCount, minOrganizationResearchFieldCount, maxOrganizationResearchFieldCount, organizationResearchFieldCountWeight);
            }
            //机构得分
            double score = add(add(add(add(add(peopleCountScore, contractCountScore), awardCountScore), patentCountScore), organizationDocumentCountScore), organizationResearchFieldCountScore);
            NumberFormat nbf = NumberFormat.getInstance();
            nbf.setMinimumFractionDigits(4);
            idwOrgMapper.updateScoreByOrgCode(org.getOrgCode(), nbf.format(score));
        }
        //计算排名
        List<IdwOrg> scoreList = idwOrgMapper.selectScoreOrgList(true);
        for (int i = 0; i < scoreList.size(); i++) {
            int ranking = i + 1;
            idwOrgMapper.updateRankingByOrgCode(scoreList.get(i).getOrgCode(), ranking);
        }
        return "计算完成！";
    }

    /**
     * 计算机构各项得分
     *
     * @param count    当前数量
     * @param minCount 最大数量
     * @param maxCount 最小数量
     * @param weight   权重
     * @return 结果
     */
    public double calculateScore(Integer count, Integer minCount, Integer maxCount, String weight) {
        //当前值
        double presentVal = sub(String.valueOf(count), String.valueOf(minCount));
        //最大值
        double maxVal = sub(String.valueOf(maxCount), String.valueOf(minCount));
        double val = div(String.valueOf(presentVal), String.valueOf(maxVal), 4);
        double mul = mul(String.valueOf(val), "100");
        //最终得分
        return mul(String.valueOf(mul), weight);
    }

    /**
     * 计算供应链评分与排名
     *
     * @return 结果
     */
    @Override
    public String calculateSupplyChainsRankingAndScore() {
        BigDecimal minAmount = idwOrgMapper.selectSupplyChainsMinAmount();//机构合同最小总金额
        BigDecimal maxAmount = idwOrgMapper.selectSupplyChainsMaxAmount();//机构合同最大总金额
        Integer minContractCount = idwOrgMapper.selectSupplyChainsMinContractCount();//机构合同最小数量
        Integer maxContractCount = idwOrgMapper.selectSupplyChainsMaxContractCount();//机构合同最大数量
        Integer minWeaponryCount = idwOrgMapper.selectSupplyChainsMinWeaponryCount();//机构合同最小装备数量
        Integer maxWeaponryCount = idwOrgMapper.selectSupplyChainsMaxWeaponryCount();//机构合同最大装备数量
        Integer minParticipateKeyNodeCount = idwOrgMapper.selectSupplyChainsMinParticipateKeyNodeCount();//机构参与装备结构关键节点最小数量
        Integer maxParticipateKeyNodeCount = idwOrgMapper.selectSupplyChainsMaxParticipateKeyNodeCount();//机构参与装备结构关键节点最大数量
        List<SupplyChainsAnalysisVO> supplyChainsAnalysisVOList = idwOrgMapper.selectUupplyChainsAnalysis();
        for (SupplyChainsAnalysisVO supplyChainsAnalysisVO : supplyChainsAnalysisVOList) {
            //合同金额
            BigDecimal amount = supplyChainsAnalysisVO.getAmount();
            double amountScore = 0;
            if (StringUtils.isNotNull(amount)) {
                amountScore = (amount.subtract(minAmount)).divide((maxAmount.subtract(minAmount)), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("0.25")).doubleValue();
            }
            //合同数量
            Integer contractCount = supplyChainsAnalysisVO.getContractCount();
            double contractCountScore = 0;
            if (StringUtils.isNotNull(contractCount) && contractCount > 0) {
                contractCountScore = calculateScore(contractCount, minContractCount, maxContractCount, "0.25");
            }
            //装备数量
            Integer weaponryCount = supplyChainsAnalysisVO.getWeaponryCount();
            double weaponryCountScore = 0;
            if (StringUtils.isNotNull(weaponryCount) && weaponryCount > 0) {
                weaponryCountScore = calculateScore(weaponryCount, minWeaponryCount, maxWeaponryCount, "0.25");
            }
            //参与关键节点
            Integer participateKeyNodeCount = idwOrgMapper.selectParticipateKeyNodeCountByOrgCode(supplyChainsAnalysisVO.getOrgCode());
            double participateKeyNodeCountScore = 0;
            if (StringUtils.isNotNull(participateKeyNodeCount) && participateKeyNodeCount > 0) {
                participateKeyNodeCountScore = calculateScore(participateKeyNodeCount, minParticipateKeyNodeCount, maxParticipateKeyNodeCount, "0.25");
            }
            //得分
            double score = add(add(amountScore, contractCountScore), add(weaponryCountScore, participateKeyNodeCountScore));
            NumberFormat nbf = NumberFormat.getInstance();
            nbf.setMinimumFractionDigits(4);
            idwOrgMapper.updateSupplyChainsScoreByOrgCode(supplyChainsAnalysisVO.getOrgCode(), nbf.format(score));
        }
        return "机构供应链评分计算完成！";
    }

    /**
     * 相加
     *
     * @param doubleValA
     * @param doubleValB
     * @return
     */
    public static double add(double doubleValA, double doubleValB) {
        BigDecimal a2 = new BigDecimal(doubleValA);
        BigDecimal b2 = new BigDecimal(doubleValB);
        return a2.add(b2).doubleValue();
    }

    /**
     * 相减
     *
     * @param doubleValA
     * @param doubleValB
     * @return
     */
    public static double sub(String doubleValA, String doubleValB) {
        BigDecimal a2 = new BigDecimal(doubleValA);
        BigDecimal b2 = new BigDecimal(doubleValB);
        return a2.subtract(b2).doubleValue();
    }

    /**
     * 相乘
     *
     * @param doubleValA
     * @param doubleValB
     * @return
     */
    public static double mul(String doubleValA, String doubleValB) {
        BigDecimal a2 = new BigDecimal(doubleValA);
        BigDecimal b2 = new BigDecimal(doubleValB);
        return a2.multiply(b2).doubleValue();
    }

    /**
     * 相除
     *
     * @param doubleValA
     * @param doubleValB
     * @param scale      除不尽时指定精度
     * @return
     */
    public static double div(String doubleValA, String doubleValB, int scale) {
        BigDecimal a2 = new BigDecimal(doubleValA);
        BigDecimal b2 = new BigDecimal(doubleValB);
        return a2.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    @Override
    public String subject() {
        return "机构";
    }

    @Override
    public Long statisticalTotalQuantity() {
        return idwOrgMapper.selectOrgCount();
    }

    @Override
    public List<StatisticsVO> statisticalItemQuantity() {
        return idwOrgMapper.loadOrgStatisticsByOrgTypeExcludeCreateUser(null);
    }

    @Override
    public List<StatisticsVO> trendAnalysis() {
        return null;
    }
}
