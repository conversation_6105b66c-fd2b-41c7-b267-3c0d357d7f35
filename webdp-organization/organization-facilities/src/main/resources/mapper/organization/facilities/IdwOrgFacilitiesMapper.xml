<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.organization.facilities.mapper.IdwOrgFacilitiesMapper">
    
    <resultMap type="com.lirong.organization.facilities.domain.IdwOrgFacilities" id="IdwOrgFacilitiesResult">
        <result property="facilitiesId"    column="facilities_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="country"    column="country"    />
        <result property="siteNameCn"    column="site_name_cn"    />
        <result property="siteNameEn"    column="site_name_en"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="status"    column="status"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="floorSpace"    column="floor_space"    />
        <result property="importantFacilities"    column="important_facilities"    />
        <result property="uses"    column="uses"    />
        <result property="remark"    column="remark"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectIdwOrgFacilitiesVo">
        SELECT
            facilities_id, org_code, country, site_name_cn,
            site_name_en, longitude, latitude, STATUS,
            start_date, end_date, floor_space, remark,
            important_facilities, uses, source
        FROM
            idw_org_facilities
        WHERE
            is_delete = 0
</sql>

    <select id="selectIdwOrgFacilitiesList" parameterType="com.lirong.organization.facilities.domain.IdwOrgFacilities" resultMap="IdwOrgFacilitiesResult">
        SELECT
            *
        FROM
        <choose>
            <when test="isPublish">
                idw_pub_org_facilities
            </when>
            <otherwise>
                idw_org_facilities
            </otherwise>
        </choose>
        <where>
            is_delete = 0
            <if test="orgCode != null  and orgCode != ''"> and org_code = #{orgCode}</if>
            <if test="siteNameCn != null  and siteNameCn != ''"> and (
                site_name_cn LIKE CONCAT( '%', #{siteNameCn}, '%' )  or site_name_en LIKE CONCAT( '%', #{siteNameCn}, '%' )
                )</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectIdwOrgFacilitiesById" resultMap="IdwOrgFacilitiesResult">
        SELECT
            *
        FROM
        <choose>
            <when test="isPublish">
                idw_pub_org_facilities
            </when>
            <otherwise>
                idw_org_facilities
            </otherwise>
        </choose>
        where
            is_delete = 0
            and facilities_id = #{facilitiesId}
    </select>

    <!--根据机构编码和名称查询-->
    <select id="seelctByOrgCodeAndName" resultMap="IdwOrgFacilitiesResult">
        <include refid="selectIdwOrgFacilitiesVo"/>
        where
            is_delete = 0
            and org_code = #{orgCode}
        <if test="siteNameCn != null and siteNameCn != ''">
            and site_name_cn = #{siteNameCn}
        </if>
        <if test="siteNameCn != null and siteNameCn != ''">
            and site_name_en = #{siteNameEn}
        </if>
    </select>

    <!--根据机构编码查询-->
    <select id="selectByOrgCodes" resultMap="IdwOrgFacilitiesResult">
        SELECT
            *
        FROM
        <choose>
            <when test="isPublish">
                idw_pub_org_facilities
            </when>
            <otherwise>
                idw_org_facilities
            </otherwise>
        </choose>
        WHERE
            is_delete = 0
        <if test="orgCodes != null and orgCodes.length > 0">
            AND org_code IN
            <foreach item="orgCode" collection="orgCodes" open="(" separator="," close=")">
                #{orgCode}
            </foreach>
        </if>
    </select>

    <insert id="insertIdwOrgFacilities" parameterType="com.lirong.organization.facilities.domain.IdwOrgFacilities" useGeneratedKeys="true" keyProperty="facilitiesId">
        insert into idw_org_facilities
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="orgCode != null and orgCode != ''">org_code,</if>
            <if test="country != null">country,</if>
            <if test="siteNameCn != null and siteNameCn != ''">site_name_cn,</if>
            <if test="siteNameEn != null">site_name_en,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="status != null">status,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="floorSpace != null">floor_space,</if>
            <if test="importantFacilities != null">important_facilities,</if>
            <if test="uses != null">uses,</if>
            <if test="remark != null">remark,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="orgCode != null and orgCode != ''">#{orgCode},</if>
            <if test="country != null">#{country},</if>
            <if test="siteNameCn != null and siteNameCn != ''">#{siteNameCn},</if>
            <if test="siteNameEn != null">#{siteNameEn},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="status != null">#{status},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="floorSpace != null">#{floorSpace},</if>
            <if test="importantFacilities != null">#{importantFacilities},</if>
            <if test="uses != null">#{uses},</if>
            <if test="remark != null">#{remark},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <!--同步数据-->
    <insert id="synchronousOrgFacilities">
        INSERT INTO
        <choose>
            <when test="isPublish">
                idw_pub_org_facilities
            </when>
            <otherwise>
                idw_org_facilities
            </otherwise>
        </choose>
        (
            org_code, country, site_name_cn, site_name_en,
            longitude, latitude, STATUS, start_date,
            end_date, floor_space, remark, important_facilities,
            uses, source, is_delete, create_by,
            create_time, update_by, update_time
        ) 
        SELECT
            org_code, country, site_name_cn, site_name_en,
            longitude, latitude, STATUS, start_date,
            end_date, floor_space, remark, important_facilities,
            uses, source, is_delete, create_by,
            create_time, update_by, update_time
        FROM
        <choose>
            <when test="!isPublish">
                idw_pub_org_facilities
            </when>
            <otherwise>
                idw_org_facilities
            </otherwise>
        </choose>
        WHERE
            is_delete = 0
            AND org_code IN
        <foreach item="orgCode" collection="orgCodes" open="(" separator="," close=")">
            #{orgCode}
        </foreach>
    </insert>

    <update id="updateIdwOrgFacilities" parameterType="com.lirong.organization.facilities.domain.IdwOrgFacilities">
        update idw_org_facilities
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgCode != null">org_code = #{orgCode},</if>
            <if test="country != null">country = #{country},</if>
            <if test="siteNameCn != null">site_name_cn = #{siteNameCn},</if>
            <if test="siteNameEn != null">site_name_en = #{siteNameEn},</if>
            <choose>
                <when test="longitude!=null and longitude != ''">
                    longitude = #{longitude},
                </when>
                <otherwise>
                    longitude = NULL,
                </otherwise>
            </choose>
            <choose>
                <when test="latitude!=null and latitude != ''">
                    latitude = #{latitude},
                </when>
                <otherwise>
                    latitude = NULL,
                </otherwise>
            </choose>
            <if test="status != null">status = #{status},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="floorSpace != null">floor_space = #{floorSpace},</if>
            <if test="importantFacilities != null">important_facilities = #{importantFacilities},</if>
            <if test="uses != null">uses = #{uses},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="source != null">source = #{source},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where facilities_id = #{facilitiesId}
    </update>

    <update id="deleteIdwOrgFacilitiesByIds">
        update idw_org_facilities
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE facilities_id in
        <foreach item="facilitiesId" collection="facilitiesIds" open="(" separator="," close=")">
            #{facilitiesId}
        </foreach>
    </update>

    <!--根据机构编码删除-->
    <update id="deleteByOrgCodes">
        update
        <choose>
            <when test="isPublish">
                idw_pub_org_facilities
            </when>
            <otherwise>
                idw_org_facilities
            </otherwise>
        </choose>
        SET update_by = #{loginName},
        update_time = sysdate(),
        org_code = CONCAT( #{deleteTime} , '-' , #{loginName} , '-' , org_code ),
        IS_DELETE = 1
        WHERE org_code in
        <foreach item="orgCode" collection="orgCodes" open="(" separator="," close=")">
            #{orgCode}
        </foreach>
    </update>

</mapper>