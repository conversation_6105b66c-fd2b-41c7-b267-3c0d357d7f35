<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改军事设施')" />
    <th:block th:include="include :: select2-css"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-facilities-edit" th:object="${idwOrgFacilities}">
            <input name="facilitiesId" th:field="*{facilitiesId}" type="hidden">
            <input name="orgCode" th:field="*{orgCode}" type="hidden">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">国家/地区：</label>
                <div class="col-sm-10">
                    <select name="country" id="countrySelect" class="form-control m-b"
                            th:with="type=${@dict.getType('sys_country')}" required>
                        <option value="" style="color: #b6b6b6" disabled selected>选择国家</option>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{country}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">中文名称：</label>
                <div class="col-sm-10">
                    <input name="siteNameCn" th:field="*{siteNameCn}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">英文名称：</label>
                <div class="col-sm-10">
                    <input name="siteNameEn" th:field="*{siteNameEn}" class="form-control" type="text">
                </div>
            </div>

            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">经度：</label>
                        <div class="col-sm-8">
                            <input name="longitude" id="longitude" onchange="calculateLongitude(this.value)" th:field="*{longitude}" placeholder="输入经纬度,系统自动计算" class="form-control" type="text" number="true">
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">纬度：</label>
                        <div class="col-sm-8">
                            <input name="latitude" id="latitude" onchange="calculateLatitude(this.value)" th:field="*{latitude}" placeholder="输入经纬度,系统自动计算" class="form-control" type="text" number="true">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">使用日期：</label>
                        <div class="col-sm-8">
                            <div class="row">
                                <div class="col-sm-5">
                                    <input name="startDate" th:field="*{startDate}" class="form-control" type="text">
                                </div>
                                <div class="col-sm-2">
                                    <label class="control-label">至</label>
                                </div>
                                <div class="col-sm-5">
                                    <input name="endDate" th:field="*{endDate}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">状态：</label>
                        <div class="col-sm-8">
                            <div class="radio-box" th:each="dict : ${@dict.getType('sys_facility_status')}">
                                <input type="radio" th:id="${'status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:field="*{status}">
                                <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-2 control-label">占地面积：</label>
                <div class="col-sm-10">
                    <textarea name="floorSpace" class="form-control" rows="4">[[*{floorSpace}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">重要设施：</label>
                <div class="col-sm-10">
                    <textarea name="importantFacilities" class="form-control" rows="4">[[*{importantFacilities}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">用途：</label>
                <div class="col-sm-10">
                    <textarea name="uses" class="form-control" rows="4">[[*{uses}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea name="remark" class="form-control" rows="4">[[*{remark}]]</textarea>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-2 control-label is-required">数据来源：</label>
                <div class="col-sm-10">
                    <input name="source" th:field="*{source}" class="form-control" type="text" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js"/>
    <script th:inline="javascript">
        let prefix = ctx + "organization/facilities";
        $("#form-facilities-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-facilities-edit').serialize());
            }
        }

        let lonLatRe = /^(-?\d+)(\.\d+)?$/;
        //计算经纬度
        function calculateLongitude(value){
            $('#longitude').val('');
            if (value.indexOf("N") > 0 || value.indexOf("S") > 0 || value.indexOf("E") > 0 || value.indexOf("W") > 0){
                if (value.indexOf("E") > 0 || value.indexOf("W") > 0){
                    $('#longitude').val(calculateLon(value));
                }
                if (value.indexOf("N") > 0 || value.indexOf("S") > 0){
                    $('#latitude').val(calculateLat(value));
                }
            } else if (lonLatRe.test(value)){
                $('#longitude').val((value + 'longitude').replace(value.substring(value.indexOf('.') + 7) + 'longitude', ''));
            } else if (value != ''){
                $.modal.msgError('格式错误！')
            }
        }
        function calculateLatitude(value){
            $('#latitude').val('');
            if (value.indexOf("N") > 0 || value.indexOf("S") > 0 || value.indexOf("E") > 0 || value.indexOf("W") > 0){
                if (value.indexOf("E") > 0 || value.indexOf("W") > 0){
                    $('#longitude').val(calculateLon(value));
                }
                if (value.indexOf("N") > 0 || value.indexOf("S") > 0){
                    $('#latitude').val(calculateLat(value));
                }
            } else if (lonLatRe.test(value)){
                $('#latitude').val((value + 'latitude').replace(value.substring(value.indexOf('.') + 7) + 'latitude', ''));
            } else if (value != ''){
                $.modal.msgError('格式错误！')
            }
        }

    </script>
</body>
</html>