
     <div class="container-div">
         <input type="hidden" value="军事设施列表">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="form-facilities">
                    <div class="select-list">
                        <input type="hidden" name="orgCode" th:value="${orgCode}">
                        <input type="hidden" name="isPublish" id="facilitiesIsPublish">
                        <ul>
                            <li>
                                <label>设施名称：</label>
                                <input type="text" name="siteNameCn"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" th:with="type=${@dict.getType('sys_facility_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-facilities', 'bootstrap-table-facilities')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-facilities', 'bootstrap-table-facilities')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar-facilities" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="organization:facilities:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="organization:facilities:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="organization:facilities:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table-facilities"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        let editFlag = [[${@permission.hasPermi('organization:facilities:edit')}]];
        let removeFlag = [[${@permission.hasPermi('organization:facilities:remove')}]];
        let statusDatas = [[${@dict.getType('sys_facility_status')}]];
        let prefix = ctx + "organization/facilities";

        if (isView){
            //隐藏工具栏数据操作按钮
            document.getElementById("toolbar-facilities").style.display="none";
        }

        $('#facilitiesIsPublish').val(isPublish)

        $(function() {
            let options = {
                id: "bootstrap-table-facilities",          // 指定表格ID
                toolbar: "toolbar-facilities",   // 指定工具栏ID
                formId: "form-facilities",
                url: prefix + "/list",
                createUrl: prefix + "/add/" + orgCode,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "军事设施",
                uniqueId: "facilitiesId",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'siteNameCn',
                    title: '设施名称'
                },
                {
                    field: 'longitude',
                    title: '经度'
                },
                {
                    field: 'latitude',
                    title: '纬度'
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'source',
                    title: '数据来源',
                    formatter: function (value, row, index) {
                        if (value != '' && value != null) {
                            let html = "";
                            var sourceArr = value.split(';');
                            if (sourceArr != null && sourceArr.length > 0){
                                let urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/;
                                for (let i = 0; i < sourceArr.length; i++) {
                                    let source = sourceArr[i];
                                    let domainName = urlReg.exec(source);//根据正则取出网址域名
                                    if (domainName != null && domainName != '') {
                                        if (i > 0){
                                            html+= '</br>'
                                        }
                                        html+= "<a target='_blank' href='" + source + "' title='" + source + "'>" + (domainName[0] != null && domainName[0] != '' ? domainName[0] : source) + "</a>";
                                    }
                                }
                                if (html != null && html != ''){
                                    return html;
                                }
                            }
                        }
                        return $.table.tooltip(value, 8);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        let actions = [];
                        if (isView) {
                            actions.push('<a class="btn btn-primary btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="editFacilities(\'' + row.facilitiesId + '\')"><i class="fa fa-search"></i>查看</a> ');
                        } else {
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.facilitiesId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.facilitiesId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function editFacilities(facilitiesId) {
            let options = {
                title: '查看军事设施',
                width: '1000',
                height: '750',
                url: prefix + "/edit/" + facilitiesId + '/' + isPublish,
                btn: ['关闭'],
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }

        function doSubmit(index, layero){
            $.modal.close(index);
        }
    </script>