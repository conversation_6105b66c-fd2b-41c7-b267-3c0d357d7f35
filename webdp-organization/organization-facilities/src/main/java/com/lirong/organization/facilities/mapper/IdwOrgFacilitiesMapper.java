package com.lirong.organization.facilities.mapper;

import java.util.List;

import com.lirong.organization.facilities.domain.IdwOrgFacilities;
import org.apache.ibatis.annotations.Param;

/**
 * 军事设施Mapper接口
 *
 * <AUTHOR>
 * @date 2021-04-06
 */
public interface IdwOrgFacilitiesMapper {
    /**
     * 查询军事设施
     *
     * @param facilitiesId 军事设施ID
     * @param isPublish    是否加载发布表
     * @return 军事设施
     */
    public IdwOrgFacilities selectIdwOrgFacilitiesById(@Param("facilitiesId") Long facilitiesId, @Param("isPublish") boolean isPublish);

    /**
     * 查询军事设施列表
     *
     * @param idwOrgFacilities 军事设施
     * @return 军事设施集合
     */
    public List<IdwOrgFacilities> selectIdwOrgFacilitiesList(IdwOrgFacilities idwOrgFacilities);

    /**
     * 新增军事设施
     *
     * @param idwOrgFacilities 军事设施
     * @return 结果
     */
    public int insertIdwOrgFacilities(IdwOrgFacilities idwOrgFacilities);

    /**
     * 修改军事设施
     *
     * @param idwOrgFacilities 军事设施
     * @return 结果
     */
    public int updateIdwOrgFacilities(IdwOrgFacilities idwOrgFacilities);

    /**
     * 批量删除军事设施
     *
     * @param facilitiesIds 需要删除的数据ID
     * @param loginName     当前登录用户
     * @return 结果
     */
    public int deleteIdwOrgFacilitiesByIds(@Param("facilitiesIds") String[] facilitiesIds, @Param("loginName") String loginName);

    /**
     * 根据机构编码删除
     *
     * @param orgCodes   机构编码
     * @param loginName  当前登录用户
     * @param deleteTime 删除时间
     * @param isPublish  是否为发布表
     * @return 结果
     */
    public int deleteByOrgCodes(@Param("orgCodes") String[] orgCodes, @Param("loginName") String loginName, @Param("deleteTime") String deleteTime, @Param("isPublish") boolean isPublish);

    /**
     * 根据机构编码和名称查询
     *
     * @param orgCode    机构编码
     * @param siteNameCn 中文名称
     * @param siteNameEn 英文名称
     * @return 结果
     */
    public IdwOrgFacilities seelctByOrgCodeAndName(@Param("orgCode") String orgCode, @Param("siteNameCn") String siteNameCn, @Param("siteNameEn") String siteNameEn);

    /**
     * 根据机构编码查询
     *
     * @param orgCodes  机构编码
     * @param isPublish 是否加载发布表
     * @return 结果
     */
    public List<IdwOrgFacilities> selectByOrgCodes(@Param("orgCodes") String[] orgCodes, @Param("isPublish") boolean isPublish);

    /**
     * 同步数据
     *
     * @param orgCodes  机构编码
     * @param isPublish 是否同步到发布表
     * @return 结果
     */
    public int synchronousOrgFacilities(@Param("orgCodes") String[] orgCodes, @Param("isPublish") boolean isPublish);
}
