package com.lirong.organization.facilities.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 军事设施对象 idw_org_facilities
 *
 * <AUTHOR>
 * @date 2021-04-06
 */
public class IdwOrgFacilities extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 军事设施ID
     */
    private Long facilitiesId;

    /**
     * 机构代码/基地代码
     */
    @Excel(name = "机构编码|基地代码")
    private String orgCode;

    /**
     * 国家/地区
     */
    @Excel(name = "国家/地区")
    private String country;

    /**
     * 设施中文名称
     */
    @Excel(name = "中文名称")
    private String siteNameCn;

    /**
     * 设施英文名称
     */
    @Excel(name = "英文名称")
    private String siteNameEn;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String latitude;

    /**
     * 状态，active-活动，inactive-非活动
     */
    @Excel(name = "状态", dictType = "sys_facility_status", combo = {"在用", "停用"})
    private String status;

    /**
     * 使用开始日期
     */
    @Excel(name = "使用开始日期")
    private String startDate;

    /**
     * 使用结束日期
     */
    @Excel(name = "使用结束日期")
    private String endDate;

    /**
     * 占地面积
     */
    @Excel(name = "占地面积")
    private String floorSpace;

    /**
     * 重要设施
     */
    @Excel(name = "重要设施")
    private String importantFacilities;

    /**
     * 用途
     */
    @Excel(name = "用途")
    private String uses;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    /**
     * 是否加载发布表
     */
    private boolean isPublish;

    public void setFacilitiesId(Long facilitiesId) {
        this.facilitiesId = facilitiesId;
    }

    public Long getFacilitiesId() {
        return facilitiesId;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public void setSiteNameCn(String siteNameCn) {
        this.siteNameCn = siteNameCn;
    }

    public String getSiteNameCn() {
        return siteNameCn;
    }

    public void setSiteNameEn(String siteNameEn) {
        this.siteNameEn = siteNameEn;
    }

    public String getSiteNameEn() {
        return siteNameEn;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public String getFloorSpace() {
        return floorSpace;
    }

    public void setFloorSpace(String floorSpace) {
        this.floorSpace = floorSpace;
    }

    public String getImportantFacilities() {
        return importantFacilities;
    }

    public void setImportantFacilities(String importantFacilities) {
        this.importantFacilities = importantFacilities;
    }

    public void setUses(String uses) {
        this.uses = uses;
    }

    public String getUses() {
        return uses;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public boolean getIsPublish() {
        return isPublish;
    }

    public void setIsPublish(boolean isPublish) {
        this.isPublish = isPublish;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("facilitiesId", getFacilitiesId())
                .append("orgCode", getOrgCode())
                .append("country", getCountry())
                .append("siteNameCn", getSiteNameCn())
                .append("siteNameEn", getSiteNameEn())
                .append("longitude", getLongitude())
                .append("latitude", getLatitude())
                .append("status", getStatus())
                .append("startDate", getStartDate())
                .append("endDate", getEndDate())
                .append("floorSpace", getFloorSpace())
                .append("importantFacilities", getImportantFacilities())
                .append("uses", getUses())
                .append("remark", getRemark())
                .append("source", getSource())
                .append("isPublish", getIsPublish())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
