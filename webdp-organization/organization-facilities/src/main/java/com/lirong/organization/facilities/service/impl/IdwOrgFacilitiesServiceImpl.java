package com.lirong.organization.facilities.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.lirong.common.utils.*;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.organization.facilities.mapper.IdwOrgFacilitiesMapper;
import com.lirong.organization.facilities.domain.IdwOrgFacilities;
import com.lirong.organization.facilities.service.IdwOrgFacilitiesService;
import com.lirong.common.core.text.Convert;

/**
 * 军事设施Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-04-06
 */
@Service
public class IdwOrgFacilitiesServiceImpl implements IdwOrgFacilitiesService {
    @Autowired
    private IdwOrgFacilitiesMapper idwOrgFacilitiesMapper;
    @Autowired//组织机构
    private IdwOrgMapper idwOrgMapper;

    /**
     * 查询军事设施
     *
     * @param facilitiesId 军事设施ID
     * @param isPublish    是否加载发布表
     * @return 军事设施
     */
    @Override
    public IdwOrgFacilities selectIdwOrgFacilitiesById(Long facilitiesId, boolean isPublish) {
        return idwOrgFacilitiesMapper.selectIdwOrgFacilitiesById(facilitiesId, isPublish);
    }

    /**
     * 查询军事设施列表
     *
     * @param idwOrgFacilities 军事设施
     * @return 军事设施
     */
    @Override
    public List<IdwOrgFacilities> selectIdwOrgFacilitiesList(IdwOrgFacilities idwOrgFacilities) {
        return idwOrgFacilitiesMapper.selectIdwOrgFacilitiesList(idwOrgFacilities);
    }

    /**
     * 新增军事设施
     *
     * @param idwOrgFacilities 军事设施
     * @return 结果
     */
    @Override
    public int insertIdwOrgFacilities(IdwOrgFacilities idwOrgFacilities) {
        idwOrgFacilities.setCreateBy(ShiroUtils.getUserName());
        idwOrgFacilities.setCreateTime(DateUtils.getNowDate());
        return idwOrgFacilitiesMapper.insertIdwOrgFacilities(idwOrgFacilities);
    }

    /**
     * 修改军事设施
     *
     * @param idwOrgFacilities 军事设施
     * @return 结果
     */
    @Override
    public int updateIdwOrgFacilities(IdwOrgFacilities idwOrgFacilities) {
        idwOrgFacilities.setUpdateBy(ShiroUtils.getUserName());
        idwOrgFacilities.setUpdateTime(DateUtils.getNowDate());
        return idwOrgFacilitiesMapper.updateIdwOrgFacilities(idwOrgFacilities);
    }

    /**
     * 删除军事设施对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwOrgFacilitiesByIds(String ids) {
        String userName = ShiroUtils.getUserName();
        return idwOrgFacilitiesMapper.deleteIdwOrgFacilitiesByIds(Convert.toStrArray(ids), userName);
    }

    /**
     * 校验Excel导入数据
     *
     * @param orgFacilitiesList 军事设施集合
     * @return 结果
     */
    @Override
    public List<String> verifyImportOrgFacilities(List<IdwOrgFacilities> orgFacilitiesList) {
        if (StringUtils.isNull(orgFacilitiesList) || orgFacilitiesList.size() < 1) {
            return null;
        }
        //处理完成军事设施数据列表
        List<IdwOrgFacilities> treatingAfterOrgFacilitiesList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        List<String> orgCodeList = (List<String>) CacheUtils.get("orgImportTreatingAfterOrgCodeList-" + ShiroUtils.getUserName());
        for (IdwOrgFacilities orgFacilities : orgFacilitiesList) {
            row++;
            if (StringUtils.isNotNull(orgFacilities)) {
                if (StringUtils.isBlank(orgFacilities.getOrgCode())) {
                    isFailure = true;
                    msgList.add("军事设施,第" + row + "行," + " 机构编码为空");
                } else {
                    IdwOrg org = idwOrgMapper.selectByUniqueCode(orgFacilities.getOrgCode());
                    if (StringUtils.isNull(org) && !(StringUtils.isNotNull(orgCodeList) && orgCodeList.size() > 0 && orgCodeList.contains(orgFacilities.getOrgCode()))) {
                        isFailure = true;
                        msgList.add("军事设施,第" + row + "行," + " 机构编码不存在");
                    }
                }
                if (StringUtils.isBlank(orgFacilities.getSiteNameCn())) {
                    isFailure = true;
                    msgList.add("军事设施,第" + row + "行," + " 中文名称为空");
                }
                if (StringUtils.isBlank(orgFacilities.getSource())) {
                    isFailure = true;
                    msgList.add("军事设施,第" + row + "行," + " 数据来源为空");
                }
                //格式化经纬度
                String latitude = orgFacilities.getLatitude();
                //纬度范围 -90~90
                if (StringUtils.isNotBlank(latitude)) {
                    if (latitude.contains("°")) {
                        latitude = CoordUtils.formatLAL(latitude);
                    }
                    //校验格式是否规范
                    boolean latitudeIsNumeric = StringUtils.validateNumber(latitude);
                    int parseIntLatitude = 0;
                    try {
                        parseIntLatitude = Integer.parseInt(latitude.split("\\.")[0]);
                        if (!latitudeIsNumeric || !(90 > parseIntLatitude && parseIntLatitude > -90)) {
                            isFailure = true;
                            msgList.add("军事设施,第" + row + "行," + " 纬度（" + orgFacilities.getLatitude() + "）格式错误（纬度范围 -90~90，保留小数点后六位）");
                        } else {
                            orgFacilities.setLatitude(latitude);
                        }
                    } catch (NumberFormatException e) {
                        isFailure = true;
                        msgList.add("军事设施,第" + row + "行," + " 纬度（" + orgFacilities.getLatitude() + "）格式错误（纬度范围 -90~90，保留小数点后六位）");
                    }
                }
                String longitude = orgFacilities.getLongitude();
                //经度范围 -180~180
                if (StringUtils.isNotBlank(longitude)) {
                    if (longitude.contains("°")) {
                        longitude = CoordUtils.formatLAL(longitude);
                    }
                    //校验格式是否规范
                    boolean longitudeIsNumeric = StringUtils.validateNumber(longitude);
                    int parseIntLongitude = 0;
                    try {
                        parseIntLongitude = Integer.parseInt(longitude.split("\\.")[0]);
                        if (!longitudeIsNumeric || !(180 > parseIntLongitude && parseIntLongitude > -180)) {
                            isFailure = true;
                            msgList.add("军事设施,第" + row + "行," + " 纬度（" + orgFacilities.getLongitude() + "）格式错误（经度范围 -180~180，保留小数点后六位）");
                        } else {
                            orgFacilities.setLongitude(longitude);
                        }
                    } catch (NumberFormatException e) {
                        isFailure = true;
                        msgList.add("军事设施,第" + row + "行," + " 纬度（" + orgFacilities.getLongitude() + "）格式错误（经度范围 -180~180，保留小数点后六位）");
                    }
                }
                treatingAfterOrgFacilitiesList.add(orgFacilities);
            }
        }

        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("orgImportTreatingAfterOrgFacilitiesList-" + userName, treatingAfterOrgFacilitiesList);
        }
        return null;
    }

    /**
     * 导入机构军事设施信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param userName      操作用户
     * @return 结果
     */
    @Override
    public String importOrgFacilities(boolean updateSupport, String userName) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwOrgFacilities> orgFacilitiesList = (List<IdwOrgFacilities>) CacheUtils.get("orgImportTreatingAfterOrgFacilitiesList-" + userName);
        if (StringUtils.isNull(orgFacilitiesList) || orgFacilitiesList.size() < 1) {
            return null;
        }
        for (IdwOrgFacilities orgFacilities : orgFacilitiesList) {
            if (StringUtils.isNotNull(orgFacilities)) {
                IdwOrg org = idwOrgMapper.selectByUniqueCode(orgFacilities.getOrgCode());
                //赋值机构编码
                orgFacilities.setOrgCode(org.getOrgCode());
                //查询数据是否存在
                IdwOrgFacilities facilities = idwOrgFacilitiesMapper.seelctByOrgCodeAndName(orgFacilities.getOrgCode(), orgFacilities.getSiteNameCn(), orgFacilities.getSiteNameEn());
                if (StringUtils.isNull(facilities)) {
                    //新增
                    insertCount++;
                    orgFacilities.setCreateBy(userName);
                    orgFacilities.setCreateTime(nowDate);
                    idwOrgFacilitiesMapper.insertIdwOrgFacilities(orgFacilities);
                } else if (updateSupport) {
                    //更新
                    updateCount++;
                    orgFacilities.setFacilitiesId(facilities.getFacilitiesId());
                    orgFacilities.setUpdateBy(userName);
                    orgFacilities.setUpdateTime(nowDate);
                    idwOrgFacilitiesMapper.updateIdwOrgFacilities(orgFacilities);
                }
            }
        }
        CacheUtils.remove("orgImportTreatingAfterOrgFacilitiesList-" + userName);
        return "军事设施共：" + orgFacilitiesList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }

    /**
     * 根据机构编码查询
     *
     * @param orgCodes 机构编码
     * @return 结果
     */
    @Override
    public List<IdwOrgFacilities> selectByOrgCodes(String[] orgCodes) {
        return idwOrgFacilitiesMapper.selectByOrgCodes(orgCodes, false);
    }

    /**
     * 根据编码删除
     *
     * @param type       类型
     * @param codes      编码
     * @param deleteTime 删除时间
     * @return 结果
     */
    @Override
    public int deleteByCode(String type, String[] codes, String deleteTime) {
        if (type.equals("org")) {
            String loginName = ShiroUtils.getUserName();
            return idwOrgFacilitiesMapper.deleteByOrgCodes(codes, loginName, deleteTime, false);
        }
        return 1;
    }

    /**
     * 根据机构编码查询
     *
     * @param type 类型 装备-weaponry，机构-organization 人员-personnel
     * @param code 编码
     * @return 结果
     */
    @Override
    public Object getDataByCodes(String type, String[] code) {
        if (!"organization".equals(type)) {
            return null;
        }
        return idwOrgFacilitiesMapper.selectByOrgCodes(code, false);
    }

    /**
     * 根据主纬度类型获取子类型标题
     *
     * @param type 类型 装备-weaponry，机构-organization 人员-personnel
     * @return 结果
     */
    @Override
    public String getTitle(String type) {
        if (!"organization".equals(type)) {
            return null;
        }
        return "org_facilities";
    }

    /**
     * 数据发布
     *
     * @param type        类型 装备-weaponry，机构-organization 人员-personnel
     * @param title       当前维度标题
     * @param code        编码
     * @param publishTime 发布时间
     */
    @Override
    public void publish(String type, String title, String[] code, String publishTime) {
        if (type.equals("organization") && title.equals("org_facilities")) {
            String userName = ShiroUtils.getUserName();
            //根据编码删除已发布数据
            idwOrgFacilitiesMapper.deleteByOrgCodes(code, userName, publishTime, true);
            //同步数据到发布表
            idwOrgFacilitiesMapper.synchronousOrgFacilities(code, true);
        }
    }

    /**
     * 根据关联类型与关联编码获取已发布数据
     *
     * @param type 类型 装备-weaponry，机构-organization 人员-personnel
     * @param code 编码
     * @return 结果
     */
    @Override
    public Object getPublishData(String type, String[] code) {
        if (!"organization".equals(type)) {
            return null;
        }
        return idwOrgFacilitiesMapper.selectByOrgCodes(code, true);
    }

    /**
     * 根据类型插入数据
     *
     * @param type       类型 装备-weaponry，机构-organization 人员-personnel
     * @param title      当前维度标题
     * @param code       编码
     * @param insertTime 插入时间
     */
    @Override
    public void insertData(String type, String title, String[] code, String insertTime) {
        if ("organization".equals(type) && title.equals("org_facilities")) {
            String userName = ShiroUtils.getUserName();
            //删除未发布数据
            idwOrgFacilitiesMapper.deleteByOrgCodes(code, userName, insertTime, false);
            //同步数据到编辑表
            idwOrgFacilitiesMapper.synchronousOrgFacilities(code, false);
        }
    }
}
