package com.lirong.organization.facilities.service;

import java.util.List;

import com.lirong.common.service.AuditDataService;
import com.lirong.common.service.CascadeDeleteService;
import com.lirong.organization.facilities.domain.IdwOrgFacilities;

/**
 * 军事设施Service接口
 *
 * <AUTHOR>
 * @date 2021-04-06
 */
public interface IdwOrgFacilitiesService extends CascadeDeleteService, AuditDataService {
    /**
     * 查询军事设施
     *
     * @param facilitiesId 军事设施ID
     * @param isPublish    是否加载发布表
     * @return 军事设施
     */
    public IdwOrgFacilities selectIdwOrgFacilitiesById(Long facilitiesId, boolean isPublish);

    /**
     * 查询军事设施列表
     *
     * @param idwOrgFacilities 军事设施
     * @return 军事设施集合
     */
    public List<IdwOrgFacilities> selectIdwOrgFacilitiesList(IdwOrgFacilities idwOrgFacilities);

    /**
     * 新增军事设施
     *
     * @param idwOrgFacilities 军事设施
     * @return 结果
     */
    public int insertIdwOrgFacilities(IdwOrgFacilities idwOrgFacilities);

    /**
     * 修改军事设施
     *
     * @param idwOrgFacilities 军事设施
     * @return 结果
     */
    public int updateIdwOrgFacilities(IdwOrgFacilities idwOrgFacilities);

    /**
     * 批量删除军事设施
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteIdwOrgFacilitiesByIds(String ids);

    /**
     * 校验Excel导入数据
     *
     * @param orgFacilitiesList 军事设施集合
     * @return 结果
     */
    public List<String> verifyImportOrgFacilities(List<IdwOrgFacilities> orgFacilitiesList);

    /**
     * 导入机构武器装备程信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param userName      操作用户
     * @return 结果
     */
    public String importOrgFacilities(boolean updateSupport, String userName);

    /**
     * 根据机构编码查询
     *
     * @param orgCodes 机构编码
     * @return 结果
     */
    public List<IdwOrgFacilities> selectByOrgCodes(String[] orgCodes);
}
