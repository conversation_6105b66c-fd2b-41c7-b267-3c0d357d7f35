package com.lirong.organization.facilities.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.organization.facilities.domain.IdwOrgFacilities;
import com.lirong.organization.facilities.service.IdwOrgFacilitiesService;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.common.core.page.TableDataInfo;

/**
 * 军事设施Controller
 *
 * <AUTHOR>
 * @date 2021-04-06
 */
@Controller
@RequestMapping("/organization/facilities")
public class IdwOrgFacilitiesController extends BaseController {
    public static final Logger log = LoggerFactory.getLogger(IdwOrgFacilitiesController.class);

    private String prefix = "organization/facilities";

    @Autowired
    private IdwOrgFacilitiesService idwOrgFacilitiesService;

    @RequiresPermissions("organization:facilities:view")
    @GetMapping("/{orgCode}")
    public String facilities(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        try {
            orgCode = URLDecoder.decode(orgCode, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("军事设施跳转列表页解析参数失败！");
        }
        mmap.put("orgCode", orgCode);
        return prefix + "/facilities";
    }

    /**
     * 查询军事设施列表
     */
    @RequiresPermissions("organization:facilities:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(IdwOrgFacilities idwOrgFacilities) {
        startPage();
        List<IdwOrgFacilities> list = idwOrgFacilitiesService.selectIdwOrgFacilitiesList(idwOrgFacilities);
        return getDataTable(list);
    }

    /**
     * 新增军事设施
     */
    @GetMapping("/add/{orgCode}")
    public String add(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        try {
            orgCode = URLDecoder.decode(orgCode, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("军事设施跳转新增页面解析机构编码失败！");
        }
        mmap.put("orgCode", orgCode);
        return prefix + "/add";
    }

    /**
     * 新增保存军事设施
     */
    @RequiresPermissions("organization:facilities:add")
    @Log(title = "军事设施", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(IdwOrgFacilities idwOrgFacilities) {
        return toAjax(idwOrgFacilitiesService.insertIdwOrgFacilities(idwOrgFacilities));
    }

    /**
     * 修改军事设施
     */
    @GetMapping(value = {"/edit/{facilitiesId}", "/edit/{facilitiesId}/{isPublish}"})
    public String edit(@PathVariable("facilitiesId") Long facilitiesId, @PathVariable(value = "isPublish", required = false) boolean isPublish, ModelMap mmap) {
        IdwOrgFacilities idwOrgFacilities = idwOrgFacilitiesService.selectIdwOrgFacilitiesById(facilitiesId, isPublish);
        mmap.put("idwOrgFacilities", idwOrgFacilities);
        return prefix + "/edit";
    }

    /**
     * 修改保存军事设施
     */
    @RequiresPermissions("organization:facilities:edit")
    @Log(title = "军事设施", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(IdwOrgFacilities idwOrgFacilities) {
        return toAjax(idwOrgFacilitiesService.updateIdwOrgFacilities(idwOrgFacilities));
    }

    /**
     * 删除军事设施
     */
    @RequiresPermissions("organization:facilities:remove")
    @Log(title = "军事设施", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(idwOrgFacilitiesService.deleteIdwOrgFacilitiesByIds(ids));
    }

    /**
     * 导出军事设施列表
     */
    @RequiresPermissions("organization:facilities:export")
    @Log(title = "军事设施", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(IdwOrgFacilities idwOrgFacilities) {
        List<IdwOrgFacilities> list = idwOrgFacilitiesService.selectIdwOrgFacilitiesList(idwOrgFacilities);
        ExcelUtil<IdwOrgFacilities> util = new ExcelUtil<IdwOrgFacilities>(IdwOrgFacilities.class);
        return util.exportExcel(list, "军事设施");
    }
}
