<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('卫星列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="form-satellite">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>卫星型号编码：</label>
                                <input type="text" name="weaponryCode"/>
                            </li>
                            <li>
                                <label>卫星分类编码：</label>
                                <input type="text" name="classificationCode"/>
                            </li>
                            <li>
                                <label>所属国家/地区：</label>
                                <input type="text" name="homeCountry"/>
                            </li>
                            <li>
                                <label>卫星中文名称：</label>
                                <input type="text" name="satelliteNameCn"/>
                            </li>
                            <li>
                                <label>卫星英文名称：</label>
                                <input type="text" name="satelliteNameEn"/>
                            </li>
                            <li>
                                <label>图片：</label>
                                <input type="text" name="picture"/>
                            </li>
                            <li>
                                <label>部署位置：</label>
                                <input type="text" name="deploymentLocation"/>
                            </li>
                            <li>
                                <label>在役数量：</label>
                                <input type="text" name="serviceQuantity"/>
                            </li>
                            <li>
                                <label>原产国/地区：</label>
                                <input type="text" name="nationalOrigin"/>
                            </li>
                            <li>
                                <label>部署年份：</label>
                                <input type="text" name="deploymentYeat"/>
                            </li>
                            <li>
                                <label>研发情况：</label>
                                <input type="text" name="researchSituation"/>
                            </li>
                            <li>
                                <label>部署情况：</label>
                                <input type="text" name="deploymentSituation"/>
                            </li>
                            <li>
                                <label>主要性能：</label>
                                <input type="text" name="mainPerformance"/>
                            </li>
                            <li>
                                <label>运用特点：</label>
                                <input type="text" name="featuredApps"/>
                            </li>
                            <li>
                                <label>排序：</label>
                                <input type="text" name="orderNum"/>
                            </li>
                            <li>
                                <label>是否删除，0-未删除，1-已删除：</label>
                                <input type="text" name="isDelete"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-satellite', 'bootstrap-table-satellite')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-satellite', 'bootstrap-table-satellite')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar-satellite" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="weaponry:satellite:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="weaponry:satellite:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="weaponry:satellite:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="weaponry:satellite:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table-satellite"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let editFlag = [[${@permission.hasPermi('weaponry:satellite:edit')}]];
        let removeFlag = [[${@permission.hasPermi('weaponry:satellite:remove')}]];
        let prefix = ctx + "weaponry/satellite";

        $(function() {
            let options = {
                id: "bootstrap-table-satellite",          // 指定表格ID
                toolbar: "toolbar-satellite",   // 指定工具栏ID
                formId: "form-satellite",
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "卫星",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'satelliteId',
                    title: '卫星ID',
                    visible: false
                },
                {
                    field: 'weaponryCode',
                    title: '卫星型号编码'
                },
                {
                    field: 'classificationCode',
                    title: '卫星分类编码'
                },
                {
                    field: 'homeCountry',
                    title: '所属国家/地区'
                },
                {
                    field: 'satelliteNameCn',
                    title: '卫星中文名称'
                },
                {
                    field: 'satelliteNameEn',
                    title: '卫星英文名称'
                },
                {
                    field: 'picture',
                    title: '图片'
                },
                {
                    field: 'deploymentLocation',
                    title: '部署位置'
                },
                {
                    field: 'serviceQuantity',
                    title: '在役数量'
                },
                {
                    field: 'nationalOrigin',
                    title: '原产国/地区'
                },
                {
                    field: 'deploymentYeat',
                    title: '部署年份'
                },
                {
                    field: 'researchSituation',
                    title: '研发情况'
                },
                {
                    field: 'deploymentSituation',
                    title: '部署情况'
                },
                {
                    field: 'mainPerformance',
                    title: '主要性能'
                },
                {
                    field: 'featuredApps',
                    title: '运用特点'
                },
                {
                    field: 'orderNum',
                    title: '排序'
                },
                {
                    field: 'source',
                    title: '数据来源'
                },
                {
                    field: 'isDelete',
                    title: '是否删除，0-未删除，1-已删除'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.satelliteId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.satelliteId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>