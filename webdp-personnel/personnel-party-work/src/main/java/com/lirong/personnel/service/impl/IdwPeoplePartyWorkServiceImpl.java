package com.lirong.personnel.service.impl;

import java.util.*;

import com.lirong.common.exception.BusinessException;
import com.lirong.common.utils.CacheUtils;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.mapper.IdwPeopleMainMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.personnel.mapper.IdwPeoplePartyWorkMapper;
import com.lirong.personnel.domain.IdwPeoplePartyWork;
import com.lirong.personnel.service.IdwPeoplePartyWorkService;
import com.lirong.common.core.text.Convert;

/**
 * 人大、政协任职经历Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
@Service
public class IdwPeoplePartyWorkServiceImpl implements IdwPeoplePartyWorkService {
    @Autowired
    private IdwPeoplePartyWorkMapper idwPeoplePartyWorkMapper;
    @Autowired//人员通用
    private IdwPeopleMainMapper idwPeopleMainMapper;

    /**
     * 查询人大、政协任职经历
     *
     * @param experienceId 人大、政协任职经历ID
     * @return 人大、政协任职经历
     */
    @Override
    public IdwPeoplePartyWork selectIdwPeoplePartyWorkById(Long experienceId) {
        IdwPeoplePartyWork idwPeoplePartyWork = idwPeoplePartyWorkMapper.selectIdwPeoplePartyWorkById(experienceId);
        String startDate = idwPeoplePartyWork.getStartDate();
        if (StringUtils.isNotEmpty(startDate)) {
            Map<String, String> dateMap = DateUtils.splitDate(startDate);
            idwPeoplePartyWork.setStartYear(dateMap.get("year"));
            idwPeoplePartyWork.setStartMonth(dateMap.get("month"));
            idwPeoplePartyWork.setStartDay(dateMap.get("day"));
        }
        String endDate = idwPeoplePartyWork.getEndDate();
        if (StringUtils.isNotEmpty(endDate)) {
            Map<String, String> dateMap = DateUtils.splitDate(endDate);
            idwPeoplePartyWork.setEndYear(dateMap.get("year"));
            idwPeoplePartyWork.setEndMonth(dateMap.get("month"));
            idwPeoplePartyWork.setEndDay(dateMap.get("day"));
        }
        return idwPeoplePartyWork;
    }

    /**
     * 查询人大、政协任职经历列表
     *
     * @param idwPeoplePartyWork 人大、政协任职经历
     * @return 人大、政协任职经历
     */
    @Override
    public List<IdwPeoplePartyWork> selectIdwPeoplePartyWorkList(IdwPeoplePartyWork idwPeoplePartyWork) {
        return idwPeoplePartyWorkMapper.selectIdwPeoplePartyWorkList(idwPeoplePartyWork);
    }

    /**
     * 新增人大、政协任职经历
     *
     * @param idwPeoplePartyWork 人大、政协任职经历
     * @return 结果
     */
    @Override
    public int insertIdwPeoplePartyWork(IdwPeoplePartyWork idwPeoplePartyWork) {
        idwPeoplePartyWork.setStartDate(DateUtils.mergeDate(idwPeoplePartyWork.getStartYear(), idwPeoplePartyWork.getStartMonth(), idwPeoplePartyWork.getStartDay()));
        idwPeoplePartyWork.setEndDate(DateUtils.mergeDate(idwPeoplePartyWork.getEndYear(), idwPeoplePartyWork.getEndMonth(), idwPeoplePartyWork.getEndDay()));

        idwPeoplePartyWork.setCreateBy(ShiroUtils.getUserName());
        idwPeoplePartyWork.setCreateTime(DateUtils.getNowDate());
        return idwPeoplePartyWorkMapper.insertIdwPeoplePartyWork(idwPeoplePartyWork);
    }

    /**
     * 修改人大、政协任职经历
     *
     * @param idwPeoplePartyWork 人大、政协任职经历
     * @return 结果
     */
    @Override
    public int updateIdwPeoplePartyWork(IdwPeoplePartyWork idwPeoplePartyWork) {
        idwPeoplePartyWork.setStartDate(DateUtils.mergeDate(idwPeoplePartyWork.getStartYear(), idwPeoplePartyWork.getStartMonth(), idwPeoplePartyWork.getStartDay()));
        idwPeoplePartyWork.setEndDate(DateUtils.mergeDate(idwPeoplePartyWork.getEndYear(), idwPeoplePartyWork.getEndMonth(), idwPeoplePartyWork.getEndDay()));

        idwPeoplePartyWork.setUpdateBy(ShiroUtils.getUserName());
        idwPeoplePartyWork.setUpdateTime(DateUtils.getNowDate());
        return idwPeoplePartyWorkMapper.updateIdwPeoplePartyWork(idwPeoplePartyWork);
    }

    /**
     * 删除人大、政协任职经历对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwPeoplePartyWorkByIds(String ids) {
        String userName = ShiroUtils.getUserName();
        return idwPeoplePartyWorkMapper.deleteIdwPeoplePartyWorkByIds(Convert.toStrArray(ids), userName);
    }

    /**
     * 校验Excel导入数据
     *
     * @param partyWorkList 人大、政协任职经历集合
     * @param type          类型 人大 政协
     * @return 结果
     */
    @Override
    public List<String> verifyImportPartyWork(List<IdwPeoplePartyWork> partyWorkList, String type) {
        if (StringUtils.isNull(partyWorkList) || partyWorkList.size() < 1) {
            return null;
        }
        //处理完成人员宗教信息数据列表
        List<IdwPeoplePartyWork> treatingAfterPeoplePartyWorkList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        List<String> peopleCodeList = (List<String>) CacheUtils.get("peopleImportTreatingAfterPeopleCodeList-" + userName);
        for (IdwPeoplePartyWork peoplePartyWork : partyWorkList) {
            row++;
            if (StringUtils.isNotNull(peoplePartyWork)) {
                if (StringUtils.isBlank(peoplePartyWork.getPeopleCode())) {
                    isFailure = true;
                    msgList.add(type + "任职经历,第" + row + "行," + " 人员编码为空");
                } else {
                    String peopleCode = peoplePartyWork.getPeopleCode();
                    IdwPeopleMain people = idwPeopleMainMapper.selectPeopleByPeopleCode(peopleCode);
                    if (StringUtils.isNull(people) && (StringUtils.isNull(peopleCodeList) || !peopleCodeList.contains(peopleCode))) {
                        isFailure = true;
                        msgList.add(type + "任职经历,第" + row + "行," + " 人员编码（" + peopleCode + "）不存在");
                    }
                }
                //处理开始/结束日期
                String oldStartDate = peoplePartyWork.getStartDate();
                if (StringUtils.isNotBlank(oldStartDate)) {
                    String startDate = DateUtils.updateDateSeparator(oldStartDate, "-");
                    if (!DateUtils.isDate(startDate)) {
                        isFailure = true;
                        msgList.add(type + "任职经历,第" + row + "行," + " 开始日期（" + oldStartDate + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    }
                    //赋值
                    peoplePartyWork.setStartDate(startDate);
                }
                String oldEndDate = peoplePartyWork.getEndDate();
                if (StringUtils.isNotBlank(oldEndDate)) {
                    String endDate = DateUtils.updateDateSeparator(oldEndDate, "-");
                    if (!DateUtils.isDate(endDate)) {
                        isFailure = true;
                        msgList.add(type + "任职经历,第" + row + "行," + " 结束日期（" + oldEndDate + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    }
                    //赋值
                    peoplePartyWork.setEndDate(endDate);
                }
                treatingAfterPeoplePartyWorkList.add(peoplePartyWork);
            }
        }
        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("peopleImportTreatingAfterPeoplePartyWorkList-" + type + userName, treatingAfterPeoplePartyWorkList);
        }
        return null;
    }

    /**
     * 导入人大、政协任职经历信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @param type          类型 人大 政协
     * @return 结果
     */
    @Override
    public String importPartyWork(boolean updateSupport, String operName, String type) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwPeoplePartyWork> partyWorkList = (List<IdwPeoplePartyWork>) CacheUtils.get("peopleImportTreatingAfterPeoplePartyWorkList-" + type + operName);
        if (StringUtils.isNull(partyWorkList) || partyWorkList.size() < 1) {
            return null;
        }
        for (IdwPeoplePartyWork peoplePartyWork : partyWorkList) {
            if (StringUtils.isNotNull(peoplePartyWork)) {
                // 判断是否存在
                IdwPeoplePartyWork partyWork = idwPeoplePartyWorkMapper.verifyIsExist(peoplePartyWork.getPeopleCode(), type, peoplePartyWork.getStartDate(), peoplePartyWork.getEndDate(), peoplePartyWork.getSessionNum(), peoplePartyWork.getLevelNation(), peoplePartyWork.getLevelProvince(), peoplePartyWork.getLevelCity(), peoplePartyWork.getLevelCounty(), peoplePartyWork.getPost());
                if (StringUtils.isNull(partyWork)) {
                    // 新增
                    insertCount++;
                    peoplePartyWork.setCreateBy(operName);
                    peoplePartyWork.setCreateTime(nowDate);
                    idwPeoplePartyWorkMapper.insertIdwPeoplePartyWork(peoplePartyWork);
                } else if (updateSupport) {
                    //更新
                    updateCount++;
                    peoplePartyWork.setExperienceId(partyWork.getExperienceId());
                    peoplePartyWork.setUpdateBy(operName);
                    peoplePartyWork.setUpdateTime(nowDate);
                    idwPeoplePartyWorkMapper.updateIdwPeoplePartyWork(peoplePartyWork);
                }
            }
        }
        CacheUtils.remove("peopleImportTreatingAfterPeoplePartyWorkList-" + type + operName);
        return type + "任职经历共：" + partyWorkList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }

    /**
     * 根据编码删除
     *
     * @param type       类型
     * @param codes      编码
     * @param loginName  当年登录用户
     * @param deleteTime 删除时间
     * @return 结果
     */
    @Override
    public int deleteByCode(String type, String[] codes, String loginName, String deleteTime) {
        if (type.equals("people")) {
            return idwPeoplePartyWorkMapper.deleteByPeopleCodes(codes, loginName, deleteTime);
        }
        return 1;
    }

    /**
     * 统计提示信息
     *
     * @return 模块名称
     */
    @Override
    public String getStatisticalTips() {
        return "人大、政协任职经历";
    }

    /**
     * 根据人员编码查询数据量
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    @Override
    public Integer getStatisticalQuantity(String peopleCode) {
        return idwPeoplePartyWorkMapper.selectCountByPeopleCode(peopleCode);
    }
}
