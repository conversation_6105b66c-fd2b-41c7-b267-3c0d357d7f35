package com.lirong.personnel.common.service;

import com.lirong.personnel.common.service.impl.IdwPeopleMainServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * AI解析功能测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AIParsingTest {

    @Autowired
    private IdwPeopleMainServiceImpl peopleMainService;

    @Test
    public void testParseEnglishBiography() {
        String profileEn = "CMDCM Miguel <PERSON>\n" +
                "Command Master Chief, Naval Base Guam\n" +
                "A native of Chicago, IL, Command Master Chief <PERSON> enlisted in the United States Navy in August 1997. " +
                "Following Recruit Training Command and Hospital Corpsman Basic School (HM \"A\" School) in Great Lakes, IL as well as " +
                "follow-on specialty training, Hospitalman Apprentice Lugo reported to Strike Fighter Squadron EIGHT TWO where he served " +
                "as an Aerospace Medical Technician (AVT) from 1998 to 2002. Advancing to HM2, he served ashore as an AVT at Naval Health " +
                "Clinic Kaneohe Bay from 2002 to 2005.\n\n" +
                "Accepted into the Submarine Independent Duty Corpsman (IDC) program, he graduated from the Navy Undersea Medical Institute " +
                "(NUMI) in Groton, CT in 2006 and reported to USS LOS ANGELES (SSN 688) in Pearl Harbor, HI, where he qualified in submarines " +
                "and served as the boat's IDC until 2010. Following his tour in LOS ANGELES, HMC Lugo was assigned to serve as the Staff IDC " +
                "for Commander, Submarine Force, U.S. Pacific Fleet in Pearl Harbor from 2010 to 2013. Returning to sea, HMCS Lugo served " +
                "as IDC in USS TEXAS (SSN 775) from 2013 to 2015.\n\n" +
                "After selection as a Master Chief in 2015, HMCM Lugo served as the Fleet Medical Master Chief at Commander, US THIRD Fleet " +
                "in San Diego, CA until 2018. Following completion of Senior Enlisted Academy and the Command Master Chief-Chief of the Boat " +
                "(CMC-COB) course, CMDCM Lugo served as Command Master Chief of Naval Hospital Guam from 2018 to 2020. His second CMC tour " +
                "was at Navy Talent Acquisition Group (NTAG) Phoenix from 2020 to 2024, where his team earned back-to-back National NTAG " +
                "of the Year titles in FY-22 and FY-23 for their recruiting excellence. He is currently serving as CMC of Naval Base Guam.\n\n" +
                "Master Chief Lugo holds a bachelor's degree in Health Sciences and is pursuing a master's degree in Leadership Sciences " +
                "from Trident University International. His awards include three Meritorious Service Medals, four Navy and Marine Corps " +
                "Commendation Medals, seven Navy and Marine Corps Achievement Medals and various other personal, unit, and campaign awards.\n\n" +
                "Last Updated: 9 April 2024";

        try {
            Map<String, Object> result = peopleMainService.parseEnglishBiography(profileEn);
            
            System.out.println("=== AI解析结果 ===");
            System.out.println("英文姓名: " + result.get("nameEn"));
            System.out.println("中文姓名: " + result.get("nameCn"));
            System.out.println("军衔: " + result.get("militaryRank"));
            System.out.println("所在机构: " + result.get("orgName"));
            System.out.println("当前职务: " + result.get("post"));
            System.out.println("出生地: " + result.get("birthplace"));
            System.out.println("毕业院校: " + result.get("graduatedUniversity"));
            System.out.println("最高学历: " + result.get("education"));
            System.out.println("最高学位: " + result.get("degree"));
            System.out.println("性别: " + result.get("gender"));
            System.out.println("国家: " + result.get("country"));
            System.out.println("军兵种: " + result.get("troopsCategory"));
            System.out.println("人员类型: " + result.get("peopleType"));
            System.out.println("工作状态: " + result.get("workStatus"));
            
            System.out.println("\n=== 教育经历 ===");
            String educationalExperiences = (String) result.get("educationalExperiences");
            if (educationalExperiences != null) {
                System.out.println("原始数据: " + educationalExperiences);
                System.out.println("是否包含换行符: " + educationalExperiences.contains("\n"));
                String[] eduLines = educationalExperiences.split("\n");
                System.out.println("共 " + eduLines.length + " 条记录：");
                for (int i = 0; i < eduLines.length; i++) {
                    System.out.println((i + 1) + ". " + eduLines[i]);
                }
            }

            System.out.println("\n=== 工作经历 ===");
            String assignments = (String) result.get("assignments");
            if (assignments != null) {
                System.out.println("原始数据: " + assignments);
                System.out.println("是否包含换行符: " + assignments.contains("\n"));
                String[] workLines = assignments.split("\n");
                System.out.println("共 " + workLines.length + " 条记录：");
                for (int i = 0; i < workLines.length; i++) {
                    System.out.println((i + 1) + ". " + workLines[i]);
                }
            }
            
            System.out.println("\n=== 荣誉奖项 ===");
            System.out.println(result.get("rewardsPunishments"));
            
            System.out.println("\n=== 中文简介 ===");
            System.out.println(result.get("profileCn"));
            
            // 验证格式和翻译质量
            System.out.println("\n=== 格式和翻译质量检查 ===");

            if (educationalExperiences != null) {
                if (containsEnglishWords(educationalExperiences)) {
                    System.out.println("⚠️ 警告：教育经历中仍包含英文词汇");
                    System.out.println("问题内容: " + educationalExperiences);
                } else {
                    System.out.println("✅ 教育经历翻译质量：通过");
                }

                validateExperienceFormat(educationalExperiences, "教育经历");
            }

            if (assignments != null) {
                if (containsEnglishWords(assignments)) {
                    System.out.println("⚠️ 警告：工作经历中仍包含英文词汇");
                    System.out.println("问题内容: " + assignments);
                } else {
                    System.out.println("✅ 工作经历翻译质量：通过");
                }

                validateExperienceFormat(assignments, "工作经历");
            }
            
            System.out.println("\n=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("AI解析测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查文本是否包含英文单词
     */
    private boolean containsEnglishWords(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        // 检查是否包含连续的英文字母（排除数字和标点）
        return text.matches(".*[a-zA-Z]{2,}.*");
    }

    /**
     * 验证经历格式是否正确（每行一条记录）
     */
    private boolean validateExperienceFormat(String text, String fieldName) {
        if (text == null || text.trim().isEmpty()) {
            System.out.println("⚠️ " + fieldName + "为空");
            return false;
        }

        String[] lines = text.split("\n");
        boolean isValid = true;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (line.isEmpty()) {
                System.out.println("⚠️ " + fieldName + "第" + (i + 1) + "行为空");
                isValid = false;
                continue;
            }

            // 检查是否包含基本的分隔符
            if (!line.contains("-")) {
                System.out.println("⚠️ " + fieldName + "第" + (i + 1) + "行格式异常（缺少分隔符）: " + line);
                isValid = false;
            }

            // 检查长度是否合理
            if (line.length() < 10) {
                System.out.println("⚠️ " + fieldName + "第" + (i + 1) + "行内容过短: " + line);
                isValid = false;
            }
        }

        if (isValid) {
            System.out.println("✅ " + fieldName + "格式验证通过，共" + lines.length + "条记录");
        }

        return isValid;
    }

    @Test
    public void testLineBreakFormatting() {
        System.out.println("=== 测试换行符格式化 ===");

        // 模拟您提供的示例数据
        String testAssignments = "1998年-2002年-攻击战斗机第八十二中队-航空医疗技术员 2002年-2005年-海军健康诊所卡内奥赫湾-航空医疗技术员 2006年-2010年-美国洛杉矶号核潜艇-潜艇独立值班医务兵 2010年-2013年-美国太平洋舰队潜艇部队司令部-医务兵主任 2013年-2015年-美国德克萨斯号核潜艇-潜艇独立值班医务兵 2015年-2018年-美国第三舰队-舰队医疗主任 2018年-2020年-海军医院关岛-指挥官 2020年-2024年-海军人才招募组凤凰城-指挥官 2024年至今-海军基地关岛-指挥官";

        System.out.println("原始数据:");
        System.out.println(testAssignments);

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = peopleMainService.getClass().getDeclaredMethod("ensureLineBreaks", String.class);
            method.setAccessible(true);
            String formatted = (String) method.invoke(peopleMainService, testAssignments);

            System.out.println("\n格式化后:");
            System.out.println(formatted);

            System.out.println("\n按行分割:");
            String[] lines = formatted.split("\n");
            for (int i = 0; i < lines.length; i++) {
                System.out.println((i + 1) + ". " + lines[i]);
            }

            System.out.println("\n共 " + lines.length + " 条记录");

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
