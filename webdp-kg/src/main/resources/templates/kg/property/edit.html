<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改本体属性')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-property-edit" th:object="${ontologyProperty}">
            <input name="propId" th:field="*{propId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">本体ID：</label>
                <div class="col-sm-8">
                    <input name="ontologyId" th:field="*{ontologyId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">属性名称：</label>
                <div class="col-sm-8">
                    <input name="propName" th:field="*{propName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">表字段名称：</label>
                <div class="col-sm-8">
                    <input name="columnName" th:field="*{columnName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">是否主键：</label>
                <div class="col-sm-8">
                    <input name="primaryKey" th:field="*{primaryKey}" class="form-control" type="text" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let prefix = ctx + "kg/property";
        $("#form-property-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-property-edit').serialize());
            }
        }
    </script>
</body>
</html>