<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改体系结构')" />
    <th:block th:include="include :: colorpicker-js"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-theme-edit" th:object="${idwOrgSystemArchitecture}">
            <input name="id" id="id" th:field="*{id}" type="hidden">
            <input name="systemId" id="systemId" th:field="*{systemId}" type="hidden">
            <input name="ancestors" th:field="*{ancestors}" type="hidden">
            <input name="type" id="type" th:field="*{type}" type="hidden">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">名称：</label>
                <div class="col-sm-8">
                    <input type="hidden" id="orgCode" name="orgCode" th:field="*{orgCode}">
                    <div class="input-group">
                        <input type="text" class="form-control" id="name" th:field="*{name}" placeholder="可通过机构名称搜索" maxlength="60" name="name" required>
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">上级主题：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input id="treeId" name="parentId" type="hidden" th:value="${parentId}"/>
                        <input class="form-control" type="text" onclick="selectArchitectureTree()" id="treeName" readonly="true" th:value="${parentName}">
                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label is-required">颜色：</label>
                        <div class="col-sm-6">
                            <input name="color" id="color" th:field="*{color}" type="hidden">
                            <div id="colorPicker" class="form-control" style="width: 20px; height: 20px; margin-top: 6px; cursor: pointer;"></div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-8">
                    <div class="form-group">
                        <label class="col-sm-5 control-label is-required">同步至：</label>
                        <div class="col-sm-7">
                            <div class="radio check-box checked">
                                <label>
                                    <input type="radio" checked="" value="true" name="synchronizationColor" id="synchronizationColorTrue"> 是
                                </label>
                            </div>
                            <div class="radio check-box">
                                <label>
                                    <input type="radio" checked="" value="false" name="synchronizationColor"> 否
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">是否显示：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_true_or_false')}">
                        <input type="radio" th:id="${'isShow_' + dict.dictCode}" name="isShow" th:value="${dict.dictValue}" th:checked="${dict.default}" th:field="*{isShow}" required>
                        <label th:for="${'isShow_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">简介：</label>
                <div class="col-sm-8">
                    <div class="file-loading">
                        <textarea name="profile" id="profile" class="form-control" rows="4">[[*{profile}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">排序号：</label>
                <div class="col-sm-8">
                    <input name="orderNum" id="orderNum" th:field="*{orderNum}" class="form-control" type="text" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-suggest-js" />
    <script th:inline="javascript">
        document.getElementById("synchronizationColorTrue").checked=true
        let prefix = ctx + "system/architecture"
        $("#form-theme-edit").validate({
            focusCleanup: true,
            rules:{
                orderNum:{
                    digits:true
                }
            }
        });

        function submitHandler() {
            let backgroundColor = document.getElementById('colorPicker').style.backgroundColor;
            if (backgroundColor !== null && backgroundColor !== '') {
                $('#color').val(rgb2hex(backgroundColor))
            }
            if ($.validate.form()) {
                $.ajax({
                    type: "POST",
                    url: prefix + "/isExistByName",
                    data: {
                        'systemId': $('#systemId').val(),
                        'parentId': $('#treeId').val(),
                        'name': $('#name').val()
                    },
                    async: false,
                    success: function(isExist) {
                        if (!isExist){
                            $.operate.save(prefix + "/edit", $('#form-theme-edit').serialize(), refreshZtree);
                        } else {
                            $.modal.msgError('体系结构已存在！')
                        }
                    }
                });
            }
        }

        function refreshZtree() {
            window.parent.queryTree()
        }

        //将 rgb 颜色字符串转换为十六进制的形式
        function rgb2hex(sRGB) {
            var pattern = /rgb\(( *\d{1,3},)( *\d{1,3},)( *\d{1,3})\)/
            if(pattern.test(sRGB)){//判断是否符合rgb格式
                //去空格
                var str = sRGB.replace(/( |rgb|\(|\))/g, "")
                var arr = str.split(",")
                var result = "#"
                for (i = 0; i < 3; i++) {
                    arr[i] = parseInt(arr[i]).toString(16)
                    while(arr[i].length<2){ //判断位数，当不满两位数时
                        arr[i]="0"+arr[i]
                    }
                    result+=arr[i]
                }
                return result
            }
            else{
                return sRGB
            }
        }

        /*体系结构修改-选择选择体系结构树*/
        function selectArchitectureTree() {
            let options = {
                title: '选择体系结构',
                width: "380",
                url: prefix + "/choiceParentArchitecture/" + $('#systemId').val() + '/' + $('#id').val(),
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }

        function doSubmit(index, layero){
            let body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            layer.close(index);
        }

        //加载机构搜索下拉框  name
        var orgBsSuggest = $("#name").bsSuggest({
            url: ctx + "organization/org/selectByKeywordAndIncludeOrgTypes?keyword=",
            getDataMethod: 'url',//获取数据的方式，url：一直从url请求；data：从 options.data 获取；firstByUrl：第一次从Url获取全部数据，之后从options.data获取
            autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
            autoMinWidth: false,//是否自动最小宽度，设为 false 则最小宽度不小于输入框宽度
            listStyle: {
                'padding-top': 0,
                'max-height': '300px',
                'max-width': '400px',
                'overflow': 'auto',
                'width': 'auto',
                'transition': '0.3s',
                '-webkit-transition': '0.3s',
                '-moz-transition': '0.3s',
                '-o-transition': '0.3s'
            },//列表的样式控制
            idField: "orgCode",//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
            keyField: "orgNameCn",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
            effectiveFields: ['orgType','orgNameCn','orgNameEn'],//设置展示字段
            effectiveFieldsAlias: {
                orgType: '机构类型',
                orgNameCn: '中文名称',
                orgNameEn: '英文名称'
            }//设置字段别名
        }).on('onSetSelectValue', function (e, keyword, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            //选择后隐藏下拉框
            $("#name").bsSuggest("hide");
            $('#orgCode').val(result.orgCode);
            $('#type').val('organization');
            $('#name').val(result.orgNameCn);
            $('#profile').val(result.profileCn != '' && result.profileCn != null ? result.profileCn : result.profileEn);
        }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
            let background = $('#name').css("background-color");
            if (background == 'rgba(255, 0, 0, 0.1)'){
                $('#orgCode').val("");
                $('#profile').val("");
                $('#type').val("classify");
                $('#name').css("background-color", 'rgb(255, 255, 255)')
            }
        });

        var color = Colorpicker.create({
            el: "colorPicker",
            color: $('#color').val(),
            change: function (elem, hex) {
                elem.style.backgroundColor = hex;
            }
        })
    </script>
</body>
</html>